plugins {
    java
}

group = "snakedl4j"
version = "1.0-SNAPSHOT"

java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

repositories {
    mavenCentral()
}

dependencies {
    implementation("org.slf4j:slf4j-simple:1.6.2")
    implementation("org.deeplearning4j:deeplearning4j-nn:1.0.0-beta7")
    implementation("org.deeplearning4j:rl4j:1.0.0-beta7@pom")
    implementation("org.nd4j:nd4j-backends:1.0.0-beta7@pom")
    implementation("org.nd4j:nd4j-cuda-10.1:1.0.0-beta7")
    implementation("org.nd4j:nd4j-native-platform:1.0.0-beta7")

    testImplementation("junit:junit:4.12")
    testImplementation("org.assertj:assertj-core:3.17.2")
}